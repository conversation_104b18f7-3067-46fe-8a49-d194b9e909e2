# My Portfolio Website

## 📋 Description

A modern and responsive portfolio website showcasing my skills, projects, and professional experience. Built with React, TypeScript, and Tailwind CSS, it features a clean design with smooth animations and a user-friendly interface.

## 🔗 [Live Demo](https://ayokanmi-adejola.netlify.app/)


## ✨ Features

- **Responsive Design**: Optimized for all devices from mobile to desktop
- **Modern UI**: Clean and professional interface using shadcn/ui components
- **Theme Switching**: Toggle between light and dark modes for comfortable viewing
- **Interactive Elements**: Smooth animations and transitions
- **Project Showcase**: Detailed project cards with links to live demos and source code
- **Skills Section**: Visual representation of technical skills
- **Contact Form**: Easy way for visitors to get in touch
- **Performance Optimized**: Fast loading times and smooth scrolling
- **Mobile-First Approach**: Ensures perfect display on all screen sizes
- **Accessible Navigation**: Intuitive menu system for both desktop and mobile users

## 🛠️ Technologies Used

[![React](https://img.shields.io/badge/React-18.3.1-61DAFB?style=flat-square&logo=react)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-3178C6?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-5.4.14-646CFF?style=flat-square&logo=vite)](https://vitejs.dev/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.11-38B2AC?style=flat-square&logo=tailwindcss)](https://tailwindcss.com/)
[![shadcn/ui](https://img.shields.io/badge/shadcn/ui-latest-000000?style=flat-square&logo=shadcnui)](https://ui.shadcn.com/)
[![next-themes](https://img.shields.io/badge/next--themes-latest-000000?style=flat-square&logo=nextdotjs)](https://github.com/pacocoursey/next-themes)
[![React Router](https://img.shields.io/badge/React_Router-6.26.2-CA4245?style=flat-square&logo=reactrouter)](https://reactrouter.com/)
[![React Query](https://img.shields.io/badge/React_Query-5.56.2-FF4154?style=flat-square&logo=reactquery)](https://tanstack.com/query/)
[![React Hook Form](https://img.shields.io/badge/React_Hook_Form-7.53.0-EC5990?style=flat-square&logo=reacthookform)](https://react-hook-form.com/)
[![Zod](https://img.shields.io/badge/Zod-3.23.8-3068B7?style=flat-square&logo=zod)](https://zod.dev/)
[![Netlify](https://img.shields.io/badge/Netlify-Deployment-00C7B7?style=flat-square&logo=netlify)](https://www.netlify.com/)
[![Git](https://img.shields.io/badge/Git-Version_Control-F05032?style=flat-square&logo=git)](https://git-scm.com/)
[![GitHub](https://img.shields.io/badge/GitHub-Repository-181717?style=flat-square&logo=github)](https://github.com/)

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

```bash
# Clone the repository
git clone https://github.com/Ayokanmi-Adejola/Portfolio-Website.git

# Navigate to the project directory
cd Portfolio-Website

# Install dependencies
npm install
# or
yarn install

# Start the development server
npm run dev
# or
yarn dev
```

The application will be available at `http://localhost:5173` (or another port if 5173 is in use).

## 📁 Project Structure

```
/
├── public/            # Static files
├── src/
│   ├── components/    # Reusable UI components
│   │   └── ui/        # shadcn/ui components
│   ├── hooks/         # Custom React hooks
│   ├── lib/           # Utility functions
│   ├── pages/         # Page components
│   ├── App.tsx        # Main application component
│   ├── App.css        # Global styles
│   ├── index.css      # Tailwind imports and theme variables
│   └── main.tsx       # Application entry point
├── index.html         # HTML template
├── tailwind.config.ts # Tailwind CSS configuration
├── tsconfig.json      # TypeScript configuration
└── package.json       # Project dependencies and scripts
```

## 🔧 Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the application for production
- `npm run build:dev` - Build the application for development
- `npm run preview` - Preview the production build locally
- `npm run lint` - Run ESLint to check for code issues

## 🎨 Theme System

The portfolio features a robust theme system with light and dark modes:

- **Automatic Detection**: Detects user's system preferences
- **Manual Toggle**: Allows users to override system preference
- **Persistent Settings**: Remembers user's theme preference
- **Seamless Transitions**: Smooth transitions between themes

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Contact & Social

[![Email](https://img.shields.io/badge/Email-adejolaayokanmi90%40gmail.com-red?style=flat-square&logo=gmail)](mailto:<EMAIL>)
[![GitHub](https://img.shields.io/badge/GitHub-Ayokanmi--Adejola-black?style=flat-square&logo=github)](https://github.com/Ayokanmi-Adejola)
[![LinkedIn](https://img.shields.io/badge/LinkedIn-Ayokanmi_Adejola-0A66C2?style=flat-square&logo=linkedin&logoColor=white)](https://linkedin.com/in/ayokanmi-adejola)
[![Twitter](https://img.shields.io/badge/Twitter-@AyoAdejola100-1DA1F2?style=flat-square&logo=x&logoColor=white)](https://twitter.com/AyoAdejola100)
[![Portfolio](https://img.shields.io/badge/Portfolio-Ayokanmi_Adejola-green?style=flat-square&logo=netlify)](https://ayokanmi-adejola.netlify.app/)
